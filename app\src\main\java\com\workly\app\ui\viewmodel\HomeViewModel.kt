package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.business.*
import com.workly.app.data.model.*
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.NotesRepository
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn

class HomeViewModel(
    private val shiftRepository: ShiftRepository,
    private val attendanceRepository: AttendanceRepository,
    private val notesRepository: NotesRepository,
    private val settingsRepository: SettingsRepository,
    private val alarmManager: com.workly.app.business.AlarmManager? = null
) : ViewModel() {

    // Business logic components
    private val multiButtonStateMachine = MultiButtonStateMachine()
    private val weeklyStatusCalculator = WeeklyStatusCalculator()
    private val attendanceCalculator = AttendanceCalculator()
    private val autoResetManager = AutoResetManager(attendanceRepository, shiftRepository)

    // State flows
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    // Active shift
    val activeShift = shiftRepository.getActiveShiftFlow()

    // Today's attendance logs
    val todayLogs = attendanceRepository.getAttendanceLogsFlow().map { allLogs ->
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault()).toString()
        DailyAttendanceLogs(today, allLogs[today] ?: emptyList())
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = DailyAttendanceLogs("", emptyList())
    )

    // Multi-function button state
    val buttonState = combine(
        todayLogs,
        activeShift,
        settingsRepository.getSettingsFlow()
    ) { logs, shift, settings ->
        multiButtonStateMachine.getCurrentState(logs, shift, settings.multiButtonMode)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = MultiButtonStateMachine.ButtonStateInfo(
            currentState = MultiButtonStateMachine.ButtonState.GO_WORK,
            canPunch = false,
            canReset = false
        )
    )

    // Weekly status data
    val weeklyStatus = combine(
        attendanceRepository.getDailyWorkStatusesFlow(),
        settingsRepository.getSettingsFlow()
    ) { workStatuses, settings ->
        weeklyStatusCalculator.getCurrentWeekStatus(workStatuses, settings.firstDayOfWeek)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = WeeklyStatusCalculator.WeeklyStatusData(
            weekStartDate = Clock.System.todayIn(TimeZone.currentSystemDefault()),
            weekEndDate = Clock.System.todayIn(TimeZone.currentSystemDefault()),
            dailyStatuses = emptyList()
        )
    )

    // Recent notes
    val recentNotes = combine(
        notesRepository.getNotesFlow(),
        activeShift
    ) { notes, shift ->
        val today = Clock.System.todayIn(TimeZone.currentSystemDefault())
        val todayDayOfWeek = getDayOfWeekString(today.dayOfWeek)

        notes.filter { note ->
            note.isActive && (
                note.shouldRemindToday(todayDayOfWeek, shift) ||
                note.associatedShiftIds.isEmpty()
            )
        }.take(3)
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )

    init {
        checkAutoReset()
    }

    /**
     * Handle multi-function button click
     */
    fun onButtonClick() {
        viewModelScope.launch {
            try {
                val currentState = buttonState.value
                val shift = activeShift.value

                if (shift == null) {
                    _error.value = "Vui lòng chọn ca làm việc trước"
                    return@launch
                }

                val log = multiButtonStateMachine.processButtonClick(currentState, shift)
                if (log != null) {
                    val today = Clock.System.todayIn(TimeZone.currentSystemDefault()).toString()
                    attendanceRepository.addAttendanceLog(today, log)

                    // Calculate work status if this is check-out
                    if (log.type == AttendanceType.CHECK_OUT) {
                        calculateAndSaveWorkStatus(today, shift)
                    }

                    // Reschedule alarms if needed
                    alarmManager?.scheduleActiveShiftAlarms()
                }
            } catch (e: Exception) {
                _error.value = "Lỗi khi xử lý: ${e.message}"
            }
        }
    }

    /**
     * Handle punch button click
     */
    fun onPunchClick() {
        viewModelScope.launch {
            try {
                val log = multiButtonStateMachine.processPunchClick()
                val today = Clock.System.todayIn(TimeZone.currentSystemDefault()).toString()
                attendanceRepository.addAttendanceLog(today, log)
            } catch (e: Exception) {
                _error.value = "Lỗi khi ký công: ${e.message}"
            }
        }
    }

    /**
     * Handle reset button click
     */
    fun onResetClick() {
        viewModelScope.launch {
            try {
                val canReset = autoResetManager.canPerformManualReset()
                if (canReset) {
                    autoResetManager.performManualReset()
                } else {
                    _error.value = "Không có dữ liệu để reset"
                }
            } catch (e: Exception) {
                _error.value = "Lỗi khi reset: ${e.message}"
            }
        }
    }

    /**
     * Handle day click in weekly grid
     */
    fun onDayClick(date: kotlinx.datetime.LocalDate) {
        // TODO: Show day details dialog
    }

    /**
     * Handle day long click in weekly grid (for manual status update)
     */
    fun onDayLongClick(date: kotlinx.datetime.LocalDate) {
        // TODO: Show manual status update dialog
    }

    private fun checkAutoReset() {
        viewModelScope.launch {
            try {
                val resetResult = autoResetManager.checkAutoReset()
                if (resetResult.shouldReset) {
                    autoResetManager.performAutoReset()
                }
            } catch (e: Exception) {
                // Log error but don't show to user
            }
        }
    }

    private suspend fun calculateAndSaveWorkStatus(date: String, shift: Shift) {
        try {
            val logs = attendanceRepository.getLogsForDate(date)
            val workStatus = attendanceCalculator.calculateDailyWorkStatus(date, shift, logs)
            attendanceRepository.saveDailyWorkStatus(workStatus)
        } catch (e: Exception) {
            _error.value = "Lỗi khi tính toán trạng thái công việc: ${e.message}"
        }
    }

    private fun getDayOfWeekString(dayOfWeek: kotlinx.datetime.DayOfWeek): String {
        return when (dayOfWeek) {
            kotlinx.datetime.DayOfWeek.MONDAY -> "Mon"
            kotlinx.datetime.DayOfWeek.TUESDAY -> "Tue"
            kotlinx.datetime.DayOfWeek.WEDNESDAY -> "Wed"
            kotlinx.datetime.DayOfWeek.THURSDAY -> "Thu"
            kotlinx.datetime.DayOfWeek.FRIDAY -> "Fri"
            kotlinx.datetime.DayOfWeek.SATURDAY -> "Sat"
            kotlinx.datetime.DayOfWeek.SUNDAY -> "Sun"
        }
    }

    fun clearError() {
        _error.value = null
    }
}
