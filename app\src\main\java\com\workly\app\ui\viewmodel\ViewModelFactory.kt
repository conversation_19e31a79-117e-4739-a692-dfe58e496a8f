package com.workly.app.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.workly.app.WorklyApplication

class ViewModelFactory(private val application: WorklyApplication) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when {
            modelClass.isAssignableFrom(MainViewModel::class.java) -> {
                MainViewModel(
                    settingsRepository = application.settingsRepository,
                    shiftRepository = application.shiftRepository,
                    attendanceRepository = application.attendanceRepository,
                    notesRepository = application.notesRepository
                ) as T
            }
            modelClass.isAssignableFrom(HomeViewModel::class.java) -> {
                HomeViewModel(
                    shiftRepository = application.shiftRepository,
                    attendanceRepository = application.attendanceRepository,
                    notesRepository = application.notesRepository,
                    settingsRepository = application.settingsRepository
                ) as T
            }
            modelClass.isAssignableFrom(ShiftManagementViewModel::class.java) -> {
                ShiftManagementViewModel(
                    shiftRepository = application.shiftRepository
                ) as T
            }
            modelClass.isAssignableFrom(SettingsViewModel::class.java) -> {
                SettingsViewModel(
                    settingsRepository = application.settingsRepository
                ) as T
            }
            modelClass.isAssignableFrom(NotesViewModel::class.java) -> {
                NotesViewModel(
                    notesRepository = application.notesRepository,
                    shiftRepository = application.shiftRepository
                ) as T
            }
            modelClass.isAssignableFrom(StatisticsViewModel::class.java) -> {
                StatisticsViewModel(
                    attendanceRepository = application.attendanceRepository,
                    shiftRepository = application.shiftRepository
                ) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}
