package com.workly.app.ui.shifts

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.workly.app.R
import com.workly.app.data.model.Shift
import com.workly.app.ui.viewmodel.AddEditShiftViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddEditShiftScreen(
    navController: NavController,
    shiftId: String? = null
) {
    val application = androidx.compose.ui.platform.LocalContext.current.applicationContext as com.workly.app.WorklyApplication
    val viewModel: AddEditShiftViewModel = viewModel(
        factory = ViewModelFactory(application)
    )
    
    val uiState by viewModel.uiState.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    val isEditing = shiftId != null
    
    LaunchedEffect(shiftId) {
        if (shiftId != null) {
            viewModel.loadShift(shiftId)
        }
    }
    
    // Handle save success
    LaunchedEffect(viewModel.saveSuccess.collectAsState().value) {
        if (viewModel.saveSuccess.value) {
            navController.popBackStack()
        }
    }
    
    // Show error snackbar
    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top bar
        TopAppBar(
            title = { 
                Text(
                    if (isEditing) stringResource(R.string.edit_shift) 
                    else stringResource(R.string.add_shift)
                ) 
            },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            },
            actions = {
                TextButton(
                    onClick = { viewModel.saveShift() },
                    enabled = !isLoading && uiState.isValid
                ) {
                    Text(stringResource(R.string.save))
                }
            }
        )
        
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Basic Information
                ShiftBasicInfoSection(
                    uiState = uiState,
                    onNameChange = viewModel::updateName,
                    onStartTimeChange = viewModel::updateStartTime,
                    onEndTimeChange = viewModel::updateEndTime,
                    onDepartureTimeChange = viewModel::updateDepartureTime,
                    onOfficeEndTimeChange = viewModel::updateOfficeEndTime
                )
                
                // Days Applied
                DaysAppliedSection(
                    selectedDays = uiState.daysApplied,
                    onDaysChange = viewModel::updateDaysApplied
                )
                
                // Reminder Settings
                ReminderSettingsSection(
                    remindBeforeStart = uiState.remindBeforeStart,
                    remindAfterEnd = uiState.remindAfterEnd,
                    onRemindBeforeStartChange = viewModel::updateRemindBeforeStart,
                    onRemindAfterEndChange = viewModel::updateRemindAfterEnd
                )
                
                // Work Settings
                WorkSettingsSection(
                    breakMinutes = uiState.breakMinutes,
                    lateThresholdMinutes = uiState.lateThresholdMinutes,
                    earlyThresholdMinutes = uiState.earlyThresholdMinutes,
                    penaltyRoundingMinutes = uiState.penaltyRoundingMinutes,
                    showPunch = uiState.showPunch,
                    onBreakMinutesChange = viewModel::updateBreakMinutes,
                    onLateThresholdChange = viewModel::updateLateThresholdMinutes,
                    onEarlyThresholdChange = viewModel::updateEarlyThresholdMinutes,
                    onPenaltyRoundingChange = viewModel::updatePenaltyRoundingMinutes,
                    onShowPunchChange = viewModel::updateShowPunch
                )
                
                // Validation Errors
                if (uiState.validationErrors.isNotEmpty()) {
                    ValidationErrorsSection(uiState.validationErrors)
                }
                
                Spacer(modifier = Modifier.height(32.dp))
            }
        }
    }
}

@Composable
private fun ShiftBasicInfoSection(
    uiState: AddEditShiftUiState,
    onNameChange: (String) -> Unit,
    onStartTimeChange: (String) -> Unit,
    onEndTimeChange: (String) -> Unit,
    onDepartureTimeChange: (String) -> Unit,
    onOfficeEndTimeChange: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Thông tin cơ bản",
                style = MaterialTheme.typography.titleMedium
            )
            
            OutlinedTextField(
                value = uiState.name,
                onValueChange = onNameChange,
                label = { Text(stringResource(R.string.shift_name)) },
                modifier = Modifier.fillMaxWidth(),
                isError = uiState.validationErrors.any { it.field == "name" }
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = uiState.departureTime,
                    onValueChange = onDepartureTimeChange,
                    label = { Text(stringResource(R.string.departure_time)) },
                    modifier = Modifier.weight(1f),
                    placeholder = { Text("07:30") },
                    isError = uiState.validationErrors.any { it.field == "departureTime" }
                )
                
                OutlinedTextField(
                    value = uiState.startTime,
                    onValueChange = onStartTimeChange,
                    label = { Text(stringResource(R.string.start_time)) },
                    modifier = Modifier.weight(1f),
                    placeholder = { Text("08:00") },
                    isError = uiState.validationErrors.any { it.field == "startTime" }
                )
            }
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = uiState.officeEndTime,
                    onValueChange = onOfficeEndTimeChange,
                    label = { Text(stringResource(R.string.office_end_time)) },
                    modifier = Modifier.weight(1f),
                    placeholder = { Text("17:00") },
                    isError = uiState.validationErrors.any { it.field == "officeEndTime" }
                )
                
                OutlinedTextField(
                    value = uiState.endTime,
                    onValueChange = onEndTimeChange,
                    label = { Text(stringResource(R.string.end_time)) },
                    modifier = Modifier.weight(1f),
                    placeholder = { Text("17:30") },
                    isError = uiState.validationErrors.any { it.field == "endTime" }
                )
            }
        }
    }
}

@Composable
private fun DaysAppliedSection(
    selectedDays: List<String>,
    onDaysChange: (List<String>) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.days_applied),
                style = MaterialTheme.typography.titleMedium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            val dayLabels = mapOf(
                "Mon" to "Thứ 2",
                "Tue" to "Thứ 3", 
                "Wed" to "Thứ 4",
                "Thu" to "Thứ 5",
                "Fri" to "Thứ 6",
                "Sat" to "Thứ 7",
                "Sun" to "Chủ nhật"
            )
            
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                dayLabels.forEach { (dayCode, dayLabel) ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Checkbox(
                            checked = selectedDays.contains(dayCode),
                            onCheckedChange = { checked ->
                                val newDays = if (checked) {
                                    selectedDays + dayCode
                                } else {
                                    selectedDays - dayCode
                                }
                                onDaysChange(newDays)
                            }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(dayLabel)
                    }
                }
            }
        }
    }
}

@Composable
private fun ReminderSettingsSection(
    remindBeforeStart: Int,
    remindAfterEnd: Int,
    onRemindBeforeStartChange: (Int) -> Unit,
    onRemindAfterEndChange: (Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Cài đặt nhắc nhở",
                style = MaterialTheme.typography.titleMedium
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = remindBeforeStart.toString(),
                    onValueChange = {
                        it.toIntOrNull()?.let { value ->
                            onRemindBeforeStartChange(value)
                        }
                    },
                    label = { Text(stringResource(R.string.remind_before_start)) },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    suffix = { Text("phút") }
                )

                OutlinedTextField(
                    value = remindAfterEnd.toString(),
                    onValueChange = {
                        it.toIntOrNull()?.let { value ->
                            onRemindAfterEndChange(value)
                        }
                    },
                    label = { Text(stringResource(R.string.remind_after_end)) },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    suffix = { Text("phút") }
                )
            }
        }
    }
}

@Composable
private fun WorkSettingsSection(
    breakMinutes: Int,
    lateThresholdMinutes: Int,
    earlyThresholdMinutes: Int,
    penaltyRoundingMinutes: Int,
    showPunch: Boolean,
    onBreakMinutesChange: (Int) -> Unit,
    onLateThresholdChange: (Int) -> Unit,
    onEarlyThresholdChange: (Int) -> Unit,
    onPenaltyRoundingChange: (Int) -> Unit,
    onShowPunchChange: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Cài đặt công việc",
                style = MaterialTheme.typography.titleMedium
            )

            OutlinedTextField(
                value = breakMinutes.toString(),
                onValueChange = {
                    it.toIntOrNull()?.let { value ->
                        onBreakMinutesChange(value)
                    }
                },
                label = { Text(stringResource(R.string.break_minutes)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                suffix = { Text("phút") }
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = lateThresholdMinutes.toString(),
                    onValueChange = {
                        it.toIntOrNull()?.let { value ->
                            onLateThresholdChange(value)
                        }
                    },
                    label = { Text(stringResource(R.string.late_threshold)) },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    suffix = { Text("phút") }
                )

                OutlinedTextField(
                    value = earlyThresholdMinutes.toString(),
                    onValueChange = {
                        it.toIntOrNull()?.let { value ->
                            onEarlyThresholdChange(value)
                        }
                    },
                    label = { Text(stringResource(R.string.early_threshold)) },
                    modifier = Modifier.weight(1f),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    suffix = { Text("phút") }
                )
            }

            OutlinedTextField(
                value = penaltyRoundingMinutes.toString(),
                onValueChange = {
                    it.toIntOrNull()?.let { value ->
                        onPenaltyRoundingChange(value)
                    }
                },
                label = { Text(stringResource(R.string.penalty_rounding)) },
                modifier = Modifier.fillMaxWidth(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                suffix = { Text("phút") }
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = showPunch,
                    onCheckedChange = onShowPunchChange
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(stringResource(R.string.show_punch))
            }
        }
    }
}

@Composable
private fun ValidationErrorsSection(
    errors: List<com.workly.app.business.ShiftValidator.ValidationError>
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Lỗi validation",
                style = MaterialTheme.typography.titleSmall,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            Spacer(modifier = Modifier.height(8.dp))

            errors.forEach { error ->
                Text(
                    text = "• ${error.message}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onErrorContainer
                )
            }
        }
    }
}

// Data class for UI state
data class AddEditShiftUiState(
    val name: String = "",
    val startTime: String = "",
    val endTime: String = "",
    val departureTime: String = "",
    val officeEndTime: String = "",
    val daysApplied: List<String> = emptyList(),
    val remindBeforeStart: Int = 15,
    val remindAfterEnd: Int = 15,
    val breakMinutes: Int = 60,
    val lateThresholdMinutes: Int = 5,
    val earlyThresholdMinutes: Int = 5,
    val penaltyRoundingMinutes: Int = 30,
    val showPunch: Boolean = false,
    val validationErrors: List<com.workly.app.business.ShiftValidator.ValidationError> = emptyList(),
    val isValid: Boolean = false
)
