package com.workly.app.data.model

import kotlinx.serialization.Serializable

@Serializable
data class Note(
    val id: String,
    val title: String,
    val content: String,
    val reminderTime: String? = null, // HH:mm format
    val associatedShiftIds: List<String> = emptyList(),
    val explicitReminderDays: List<String> = emptyList(), // Specific days if not following shift schedule
    val isActive: Boolean = true,
    val createdAt: String,
    val updatedAt: String
) {
    companion object {
        fun createNew(
            title: String,
            content: String,
            reminderTime: String? = null,
            associatedShiftIds: List<String> = emptyList(),
            explicitReminderDays: List<String> = emptyList()
        ): Note {
            val now = kotlinx.datetime.Clock.System.now().toString()
            return Note(
                id = java.util.UUID.randomUUID().toString(),
                title = title,
                content = content,
                reminderTime = reminderTime,
                associatedShiftIds = associatedShiftIds,
                explicitReminderDays = explicitReminderDays,
                createdAt = now,
                updatedAt = now
            )
        }
    }
    
    fun shouldRemindToday(dayOfWeek: String, activeShift: Shift?): Boolean {
        if (!isActive || reminderTime == null) return false
        
        // If explicit days are set, use them
        if (explicitReminderDays.isNotEmpty()) {
            return explicitReminderDays.contains(dayOfWeek)
        }
        
        // If associated with shifts, check if any shift applies today
        if (associatedShiftIds.isNotEmpty() && activeShift != null) {
            return associatedShiftIds.contains(activeShift.id) && activeShift.isApplicableToday(dayOfWeek)
        }
        
        return false
    }
}
