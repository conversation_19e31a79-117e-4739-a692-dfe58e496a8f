package com.workly.app.ui.home

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.workly.app.R
import com.workly.app.ui.home.components.MultiFunctionButton
import com.workly.app.ui.home.components.WeeklyStatusGrid
import com.workly.app.ui.home.components.WorkNotesSection
import com.workly.app.ui.viewmodel.HomeViewModel
import com.workly.app.ui.viewmodel.MainViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    navController: NavController,
    mainViewModel: MainViewModel
) {
    val application = androidx.compose.ui.platform.LocalContext.current.applicationContext as com.workly.app.WorklyApplication
    val homeViewModel: HomeViewModel = viewModel(
        factory = ViewModelFactory(application)
    )

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Top bar with date/time and settings
            TopBarSection(
                onSettingsClick = { navController.navigate("settings") }
            )
        }

        item {
            // Weather warning section (if applicable)
            WeatherWarningSection()
        }

        item {
            // Current shift info
            CurrentShiftSection(
                onSelectShiftClick = { navController.navigate("shifts") }
            )
        }

        item {
            // Multi-function button
            MultiFunctionButton(
                onButtonClick = { homeViewModel.onButtonClick() },
                onPunchClick = { homeViewModel.onPunchClick() },
                onResetClick = { homeViewModel.onResetClick() }
            )
        }

        item {
            // Button history
            ButtonHistorySection()
        }

        item {
            // Weekly status grid
            WeeklyStatusGrid(
                onDayClick = { date -> homeViewModel.onDayClick(date) },
                onDayLongClick = { date -> homeViewModel.onDayLongClick(date) }
            )
        }

        item {
            // Work notes section
            WorkNotesSection(
                onAddNoteClick = { navController.navigate("notes") },
                onEditNoteClick = { noteId -> /* TODO: Navigate to edit note */ }
            )
        }

        item {
            // Navigation buttons
            NavigationButtonsSection(navController)
        }
    }
}

@Composable
private fun TopBarSection(
    onSettingsClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = stringResource(R.string.home_title),
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = getCurrentDateTime(),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        IconButton(
            onClick = onSettingsClick
        ) {
            Icon(
                imageVector = Icons.Default.Settings,
                contentDescription = stringResource(R.string.nav_settings)
            )
        }
    }
}

@Composable
private fun WeatherWarningSection() {
    // TODO: Implement weather warning display
    // This would show weather alerts when enabled
}

@Composable
private fun CurrentShiftSection(
    onSelectShiftClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = stringResource(R.string.current_shift),
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = stringResource(R.string.no_active_shift),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(12.dp))
            Button(
                onClick = onSelectShiftClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(stringResource(R.string.select_shift))
            }
        }
    }
}

@Composable
private fun ButtonHistorySection() {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Lịch sử hôm nay",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))

            // TODO: Display actual button history
            Text(
                text = "Chưa có hoạt động nào",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun NavigationButtonsSection(navController: NavController) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        OutlinedButton(
            onClick = { navController.navigate("notes") },
            modifier = Modifier.weight(1f).padding(end = 8.dp)
        ) {
            Text(stringResource(R.string.nav_notes))
        }
        OutlinedButton(
            onClick = { navController.navigate("statistics") },
            modifier = Modifier.weight(1f).padding(start = 8.dp)
        ) {
            Text(stringResource(R.string.nav_statistics))
        }
    }
}

private fun getCurrentDateTime(): String {
    // TODO: Implement proper date/time formatting based on user settings
    return "Hôm nay, ${kotlinx.datetime.Clock.System.now().toString().substring(0, 10)}"
}
