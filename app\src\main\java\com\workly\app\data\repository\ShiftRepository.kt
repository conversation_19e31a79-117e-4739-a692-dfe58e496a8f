package com.workly.app.data.repository

import com.google.gson.reflect.TypeToken
import com.workly.app.data.model.Shift
import com.workly.app.data.storage.DataStoreManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

class ShiftRepository(private val dataStoreManager: DataStoreManager) {
    
    private val shiftListType = object : TypeToken<List<Shift>>() {}
    
    suspend fun saveShifts(shifts: List<Shift>) {
        dataStoreManager.storeData(DataStoreManager.SHIFT_LIST_KEY, shifts)
    }
    
    suspend fun getShifts(): List<Shift> {
        return dataStoreManager.getData(
            DataStoreManager.SHIFT_LIST_KEY,
            shiftListType,
            emptyList()
        )
    }
    
    fun getShiftsFlow(): Flow<List<Shift>> {
        return dataStoreManager.getDataFlow(
            DataStoreManager.SHIFT_LIST_KEY,
            shiftListType,
            emptyList()
        )
    }
    
    suspend fun addShift(shift: Shift) {
        val currentShifts = getShifts().toMutableList()
        currentShifts.add(shift)
        saveShifts(currentShifts)
    }
    
    suspend fun updateShift(updatedShift: Shift) {
        val currentShifts = getShifts().toMutableList()
        val index = currentShifts.indexOfFirst { it.id == updatedShift.id }
        if (index != -1) {
            currentShifts[index] = updatedShift.copy(
                updatedAt = kotlinx.datetime.Clock.System.now().toString()
            )
            saveShifts(currentShifts)
        }
    }
    
    suspend fun deleteShift(shiftId: String) {
        val currentShifts = getShifts().toMutableList()
        currentShifts.removeAll { it.id == shiftId }
        saveShifts(currentShifts)
        
        // If this was the active shift, clear the active shift ID
        val activeShiftId = getActiveShiftId()
        if (activeShiftId == shiftId) {
            clearActiveShift()
        }
    }
    
    suspend fun getShiftById(shiftId: String): Shift? {
        return getShifts().find { it.id == shiftId }
    }
    
    suspend fun isShiftNameExists(name: String, excludeId: String? = null): Boolean {
        return getShifts().any { it.name == name && it.id != excludeId }
    }
    
    // Active shift management
    suspend fun setActiveShift(shiftId: String) {
        dataStoreManager.storeString(DataStoreManager.ACTIVE_SHIFT_ID_KEY, shiftId)
    }
    
    suspend fun getActiveShiftId(): String? {
        val id = dataStoreManager.getString(DataStoreManager.ACTIVE_SHIFT_ID_KEY)
        return if (id.isEmpty()) null else id
    }
    
    fun getActiveShiftIdFlow(): Flow<String?> {
        return dataStoreManager.getStringFlow(DataStoreManager.ACTIVE_SHIFT_ID_KEY).combine(
            getShiftsFlow()
        ) { activeId, shifts ->
            if (activeId.isEmpty()) null
            else if (shifts.any { it.id == activeId }) activeId
            else null // Active shift was deleted
        }
    }
    
    suspend fun getActiveShift(): Shift? {
        val activeShiftId = getActiveShiftId()
        return if (activeShiftId != null) {
            getShiftById(activeShiftId)
        } else null
    }
    
    fun getActiveShiftFlow(): Flow<Shift?> {
        return getActiveShiftIdFlow().combine(getShiftsFlow()) { activeId, shifts ->
            if (activeId != null) {
                shifts.find { it.id == activeId }
            } else null
        }
    }
    
    suspend fun clearActiveShift() {
        dataStoreManager.clearKey(DataStoreManager.ACTIVE_SHIFT_ID_KEY)
    }
    
    // Utility methods
    suspend fun getShiftsForDay(dayOfWeek: String): List<Shift> {
        return getShifts().filter { it.isApplicableToday(dayOfWeek) }
    }
    
    suspend fun getActiveShiftForDay(dayOfWeek: String): Shift? {
        val activeShift = getActiveShift()
        return if (activeShift?.isApplicableToday(dayOfWeek) == true) {
            activeShift
        } else null
    }
}
